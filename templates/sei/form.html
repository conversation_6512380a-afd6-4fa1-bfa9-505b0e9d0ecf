{% load static %}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEI - GERADOR DE PROTOCOLO</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- jQuery (required for Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Brand Colors (for minor elements & accents)
                        'primary': '#FF161F',        // Vermelho (Pantone 485 C) - Errors & critical actions only
                        'primary-light': '#ffeeee',  // Light red for error backgrounds
                        'secondary': '#034EA2',      // Azul (Pantone 2955 C) - Navigation & focus
                        'accent': '#0B9247',         // Verde (Pantone 347 C) - Success & positive actions
                        'accent-dark': '#047857',    // Darker green
                        'highlight': '#FBB900',      // Amarelo (Pantone 123 C) - Highlights

                        // Neutrals (for major elements)
                        'gray-darkest': '#000000',   // Headers & primary text
                        'gray-darker': '#333333',    // Secondary text
                        'gray-dark': '#4b5563',      // Medium elements
                        'gray-medium': '#6b7280',    // Neutral elements
                        'gray-light': '#9ca3af',     // Light elements
                        'gray-lighter': '#d1d5db',   // Very light elements
                        'gray-lightest': '#f3f4f6'   // Backgrounds
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        /* Basic Select2 styling - minimal approach */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            height: 42px !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 26px !important;
            padding-left: 0 !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
            right: 8px !important;
        }

        .select2-container--default.select2-container--focus .select2-selection--single {
            border-color: #034EA2 !important;
            box-shadow: 0 0 0 1px #034EA2 !important;
        }



        /* Step Indicator Animation */
        .step-indicator {
            transition: all 0.3s ease-in-out;
        }

        /* Step Connector Lines - Responsive Design */
        .step-connector {
            position: relative;
        }

        /* Hide connecting lines on mobile screens (sm and below) */
        .step-connector::after {
            display: none;
        }

        /* Show connecting lines on medium screens and above */
        @media (min-width: 768px) {
            .step-connector::after {
                content: '';
                display: block;
                position: absolute;
                top: 20px; /* Position at center of 40px (w-10 h-10) circle */
                left: calc(50% + 20px); /* Start from right edge of circle (20px radius) */
                width: calc(100% - 40px); /* Width to connect to next circle (minus both circle edges) */
                height: 2px;
                background-color: #d1d5db; /* gray-lighter */
                z-index: 1;
            }

            .step-connector.completed::after {
                background-color: #0B9247; /* accent green for completed connections */
            }

            .step-connector.current::after {
                background-color: #034EA2; /* secondary blue for current connection */
            }

            .step-connector:last-child::after {
                display: none; /* Hide connector line after the last step */
            }
        }

        /* Step Navigation Enhancement */
        .step-nav-item {
            transition: all 0.2s ease-in-out;
        }

        .step-nav-item.disabled {
            cursor: not-allowed !important;
            opacity: 0.5;
        }

        .step-nav-item.disabled:hover {
            cursor: not-allowed !important;
            transform: none;
        }

        .step-nav-item:not(.disabled):hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Button Disabled State */
        button:disabled {
            cursor: not-allowed !important;
            pointer-events: auto !important;
        }

        button:disabled:hover {
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Form Animation */
        .form-step {
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .form-step.hidden {
            opacity: 0;
            transform: translateX(20px);
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #FF161F;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #E6141C;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-gray-darkest shadow-lg border-b border-gray-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col sm:flex-row justify-between items-center py-3 sm:py-4 gap-3 sm:gap-0">
                <div class="flex flex-col sm:flex-row items-center">
                    <h1 class="text-xl sm:text-2xl font-bold text-white">Sistema GERPRO</h1>
                    <span class="sm:ml-4 text-sm sm:text-base text-gray-light">Gerador de Protocolo</span>
                </div>
                <div class="flex items-center space-x-2 sm:space-x-4">
                    <a href="{% url 'sei:protocolo_list' %}"
                       class="inline-flex items-center px-3 sm:px-4 py-2 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors">
                        <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                        <span class="hidden sm:inline">Ver Lista</span>
                        <span class="sm:hidden">Lista</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div x-data="seiForm()">
            <!-- Page Header -->
            <div class="text-center mb-6 sm:mb-8">
                <div class="flex grow-0 justify-center py-2 items-center">
                    <img class="w-64 sm:w-80 md:w-96" src="{% static 'images/sei/logo-artesp-opt.png' %}" />
                </div>
                <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 px-4">GERPRO - GERADOR DE PROTOCOLO</h1>
                <p class="text-sm sm:text-base text-gray-600 px-4">Preencha as informações em 11 etapas para completar o cadastro</p>
            </div>



        <!-- Step Indicators -->
        <div class="relative mb-6 sm:mb-8">
            <!-- Mobile Layout: Multi-row (sm and below) -->
            <div class="block md:hidden px-2">
                <!-- Row 1: Steps 1-6 -->
                <div class="flex justify-between items-start mb-6">
                    <template x-for="(step, index) in steps.slice(0, 6)" :key="index">
                        <div class="flex flex-col items-center w-16">
                            <div class="step-indicator step-nav-item w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium mb-2 relative z-10 touch-manipulation"
                                 :class="{
                                     'bg-secondary text-white cursor-pointer': index === currentStep,
                                     'bg-accent text-white cursor-pointer': index < currentStep && isStepValid(index),
                                     'bg-gray-lighter text-gray-medium disabled': index > currentStep,
                                     'bg-primary text-white cursor-pointer': index < currentStep && !isStepValid(index)
                                 }"
                                 @click="navigateToStep(index)">
                                <span x-show="index < currentStep && isStepValid(index)" class="text-xs">✓</span>
                                <span x-show="!(index < currentStep && isStepValid(index))" x-text="index + 1" class="text-xs"></span>
                            </div>
                            <span class="text-xs text-center leading-tight truncate w-full"
                                  :class="{
                                      'text-secondary font-normal': index === currentStep,
                                      'text-accent font-normal': index < currentStep && isStepValid(index),
                                      'text-gray-medium font-normal': index > currentStep,
                                      'text-primary font-normal': index < currentStep && !isStepValid(index)
                                  }"
                                  :title="step.title"
                                  x-text="step.title"></span>
                        </div>
                    </template>
                </div>

                <!-- Row 2: Steps 7-11 -->
                <div class="flex justify-between items-start">
                    <template x-for="(step, index) in steps.slice(6)" :key="index + 6">
                        <div class="flex flex-col items-center w-16">
                            <div class="step-indicator step-nav-item w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium mb-2 relative z-10 touch-manipulation"
                                 :class="{
                                     'bg-secondary text-white cursor-pointer': (index + 6) === currentStep,
                                     'bg-accent text-white cursor-pointer': (index + 6) < currentStep && isStepValid(index + 6),
                                     'bg-gray-lighter text-gray-medium disabled': (index + 6) > currentStep,
                                     'bg-primary text-white cursor-pointer': (index + 6) < currentStep && !isStepValid(index + 6)
                                 }"
                                 @click="navigateToStep(index + 6)">
                                <span x-show="(index + 6) < currentStep && isStepValid(index + 6)" class="text-xs">✓</span>
                                <span x-show="!((index + 6) < currentStep && isStepValid(index + 6))" x-text="index + 7" class="text-xs"></span>
                            </div>
                            <span class="text-xs text-center leading-tight truncate w-full"
                                  :class="{
                                      'text-secondary font-normal': (index + 6) === currentStep,
                                      'text-accent font-normal': (index + 6) < currentStep && isStepValid(index + 6),
                                      'text-gray-medium font-normal': (index + 6) > currentStep,
                                      'text-primary font-normal': (index + 6) < currentStep && !isStepValid(index + 6)
                                  }"
                                  :title="step.title"
                                  x-text="step.title"></span>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Desktop/Tablet Layout: Single row with connecting lines (md and above) -->
            <div class="hidden md:block overflow-x-auto custom-scrollbar">
                <div class="flex justify-between items-start">
                    <template x-for="(step, index) in steps" :key="index">
                        <div class="flex flex-col items-center w-20 md:w-24 lg:w-20 relative step-connector"
                             :class="{
                                 'completed': index < currentStep && isStepValid(index),
                                 'current': index === currentStep
                             }">
                            <div class="step-indicator step-nav-item w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium mb-3 relative z-10 touch-manipulation"
                                 :class="{
                                     'bg-secondary text-white cursor-pointer': index === currentStep,
                                     'bg-accent text-white cursor-pointer': index < currentStep && isStepValid(index),
                                     'bg-gray-lighter text-gray-medium disabled': index > currentStep,
                                     'bg-primary text-white cursor-pointer': index < currentStep && !isStepValid(index)
                                 }"
                                 @click="navigateToStep(index)">
                                <span x-show="index < currentStep && isStepValid(index)" class="text-sm">✓</span>
                                <span x-show="!(index < currentStep && isStepValid(index))" x-text="index + 1" class="text-sm"></span>
                            </div>
                            <span class="text-xs lg:text-xs text-center leading-tight truncate w-full"
                                  :class="{
                                      'text-secondary font-normal': index === currentStep,
                                      'text-accent font-normal': index < currentStep && isStepValid(index),
                                      'text-gray-medium font-normal': index > currentStep,
                                      'text-primary font-normal': index < currentStep && !isStepValid(index)
                                  }"
                                  :title="step.title"
                                  x-text="step.title"></span>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Error Display -->
        <div x-show="errors.length > 0" class="mb-6 bg-primary-light border border-primary rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Erro na validação</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <template x-for="error in errors" :key="error">
                                <li x-text="error"></li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Container -->
        <div class="bg-white rounded-lg shadow-lg p-4 sm:p-6 mb-4 sm:mb-6">
            <!-- Step 0: Requisitante (Usuario) -->
            <div x-show="currentStep === 0" class="form-step">
                <h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm mr-2 sm:mr-3">1</span>
                    <span class="text-lg sm:text-2xl">Informações do Requisitante</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="nome" class="block text-sm font-medium text-gray-700 mb-2">
                            Nome Completo <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="nome"
                               x-model="formData.requisitante.nome"
                               @input="formatNomeInput($event)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                               :class="{'border-primary': errors.some(error => error.includes('Nome') || error.includes('nome'))}"
                               placeholder="Digite seu nome completo"
                               style="text-transform: uppercase;"
                               maxlength="150">
                        <p class="text-xs text-gray-500 mt-1">
                            💡 Mínimo 3 caracteres, máximo 150.
                            <span x-show="formData.requisitante.nome.trim()"
                                  x-text="`(${formData.requisitante.nome.trim().length}/150)`"
                                  :class="formData.requisitante.nome.trim().length > 150 ? 'text-red-500' : 'text-gray-500'"></span>
                        </p>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            E-mail <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email"
                               x-model="formData.requisitante.email"
                               @input="formatEmailInput($event)"
                               @paste.prevent
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                               :class="{'border-primary': errors.includes('E-mail é obrigatório') || errors.includes('E-mail inválido') || errors.includes('E-mails não coincidem')}"
                               placeholder="<EMAIL>"
                               style="text-transform: lowercase;">
                        <p class="text-xs text-red-500 mt-1">⚠️ Colar não permitido - digite manualmente</p>
                    </div>

                    <div>
                        <label for="email_confirm" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirmar E-mail <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email_confirm"
                               x-model="formData.requisitante.email_confirm"
                               @input="formatEmailConfirmInput($event)"
                               @paste.prevent
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                               :class="{'border-primary': errors.includes('Confirmação de e-mail é obrigatória') || errors.includes('E-mails não coincidem')}"
                               placeholder="Confirme seu e-mail"
                               style="text-transform: lowercase;">
                        <p class="text-xs text-red-500 mt-1">⚠️ Colar não permitido - digite manualmente</p>
                    </div>
                </div>
            </div>

            <!-- Step 1: Unidade -->
            <div x-show="currentStep === 1" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                    Unidade
                </h2>

                <div class="w-full">
                    <label for="unidade_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione uma Unidade <span class="text-red-500">*</span>
                    </label>
                    <select id="unidade_select"
                            class="w-full select2-unidade"
                            :class="{'border-primary': errors.includes('Seleção de unidade é obrigatória')}"
                            :disabled="formData.unidade.notApplicable">
                        <option value="">Selecione uma unidade ou digite para buscar</option>
                    </select>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                                   x-model="formData.unidade.notApplicable"
                                   @change="handleNotApplicableChange('unidade', $event.target.checked)"
                                   class="form-checkbox h-4 w-4 text-secondary focus:ring-secondary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Não se aplica</span>
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar</p>
                </div>
            </div>

            <!-- Step 2: Interessado -->
            <div x-show="currentStep === 2" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                    Interessado
                </h2>

                <div class="w-full">
                    <label for="interessado_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione um Interessado <span class="text-red-500">*</span>
                    </label>
                    <select id="interessado_select"
                            class="w-full select2-interessado"
                            :class="{'border-primary': errors.includes('Seleção de interessado é obrigatória')}"
                            :disabled="formData.interessado.notApplicable">
                        <option value="">Selecione um interessado ou digite para buscar</option>
                    </select>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                                   x-model="formData.interessado.notApplicable"
                                   @change="handleNotApplicableChange('interessado', $event.target.checked)"
                                   class="form-checkbox h-4 w-4 text-secondary focus:ring-secondary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Não se aplica</span>
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/concessão.</p>
                </div>
            </div>

            <!-- Step 3: Localização -->
            <div x-show="currentStep === 3" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">4</span>
                    Localização
                </h2>

                <div class="w-full">
                    <label for="localizacao_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione uma Localização <span class="text-red-500">*</span>
                    </label>
                    <select id="localizacao_select"
                            class="w-full select2-localizacao"
                            :class="{'border-primary': errors.includes('Seleção de localização é obrigatória')}"
                            :disabled="formData.localizacao.notApplicable">
                        <option value="">Selecione uma localização ou digite para buscar</option>
                    </select>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                                   x-model="formData.localizacao.notApplicable"
                                   @change="handleNotApplicableChange('localizacao', $event.target.checked)"
                                   class="form-checkbox h-4 w-4 text-secondary focus:ring-secondary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Não se aplica</span>
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/localização.</p>
                </div>
            </div>

            <!-- Step 4: Assunto -->
            <div x-show="currentStep === 4" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">5</span>
                    Assunto
                </h2>

                <div class="w-full">
                    <label for="assunto_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione um Assunto <span class="text-red-500">*</span>
                    </label>
                    <select id="assunto_select"
                            class="w-full select2-assunto"
                            :class="{'border-primary': errors.includes('Seleção de assunto é obrigatória')}">
                        <option value="">Selecione um assunto ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/documento.</p>
                </div>
            </div>

            <!-- Step 5: Serviço -->
            <div x-show="currentStep === 5" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">6</span>
                    Serviço
                </h2>

                <div class="bg-green-50 border border-accent rounded-lg p-4 mb-6">
                    <p class="text-accent-dark text-sm">
                        <strong>Campos Opcionais:</strong> Ambos os campos são opcionais.
                        Você pode prosseguir sem preencher nenhum campo.
                    </p>
                </div>

                <!-- Código field in its own container -->
                <div class="max-w-md mb-6">
                    <label for="servico_codigo" class="block text-sm font-medium text-gray-700 mb-2">
                        Código
                    </label>
                    <input type="text"
                           id="servico_codigo"
                           x-model="formData.servico.codigo"
                           @input="validateServicoCodigoInput($event); saveToSession()"
                           @keypress="validateNumericInput($event)"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                           :class="{'border-primary': errors.includes('Código do serviço deve conter apenas números')}"
                           placeholder="Digite o código (apenas números, opcional)">
                    <p class="text-xs text-gray-500 mt-1">⚠️ Apenas números são permitidos</p>
                </div>

                <!-- Tipo de Serviço field - Full width on its own line -->
                <div class="w-full">
                    <label for="servico_tipo_servico" class="block text-sm font-medium text-gray-700 mb-2">
                        Tipo de Serviço
                    </label>
                    <input type="text"
                           id="servico_tipo_servico"
                           x-model="formData.servico.tipo_servico"
                           @input="formatServicoTipoInput($event)"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                           :class="{'border-primary': errors.some(error => error.includes('Tipo de serviço') || error.includes('tipo de serviço'))}"
                           placeholder="Digite o tipo de serviço (opcional)"
                           style="text-transform: uppercase;"
                           maxlength="255">
                    <p class="text-xs text-gray-500 mt-1">
                        💡 Campo opcional. Se preenchido: mínimo 3 caracteres, máximo 255. Espaços são permitidos.
                        <span x-show="formData.servico.tipo_servico.trim()"
                              x-text="`(${formData.servico.tipo_servico.trim().length}/255)`"
                              :class="formData.servico.tipo_servico.trim().length > 255 ? 'text-red-500' : 'text-gray-500'"></span>
                    </p>
                </div>
            </div>

            <!-- Step 6: Local -->
            <div x-show="currentStep === 6" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">7</span>
                    Local
                </h2>

                <div class="w-full">
                    <label for="local_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione um Local <span class="text-red-500">*</span>
                    </label>
                    <select id="local_select"
                            class="w-full select2-local"
                            :class="{'border-primary': errors.includes('Seleção de local é obrigatória')}"
                            :disabled="formData.local.notApplicable">
                        <option value="">Selecione um local ou digite para buscar</option>
                    </select>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                                   x-model="formData.local.notApplicable"
                                   @change="handleNotApplicableChange('local', $event.target.checked)"
                                   class="form-checkbox h-4 w-4 text-secondary focus:ring-secondary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Não se aplica</span>
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/local.</p>
                </div>
            </div>

            <!-- Step 7: Disciplina -->
            <div x-show="currentStep === 7" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">8</span>
                    Disciplina
                </h2>

                <div class="w-full">
                    <label for="disciplina_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione uma Disciplina <span class="text-red-500">*</span>
                    </label>
                    <select id="disciplina_select"
                            class="w-full select2-disciplina"
                            :class="{'border-primary': errors.includes('Seleção de disciplina é obrigatória')}"
                            :disabled="formData.disciplina.notApplicable">
                        <option value="">Selecione uma disciplina ou digite para buscar</option>
                    </select>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox"
                                   x-model="formData.disciplina.notApplicable"
                                   @change="handleNotApplicableChange('disciplina', $event.target.checked)"
                                   class="form-checkbox h-4 w-4 text-secondary focus:ring-secondary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Não se aplica</span>
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/disciplina.</p>
                </div>
            </div>

            <!-- Step 8: Revisão -->
            <div x-show="currentStep === 8" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">9</span>
                    Revisão do Documento
                </h2>

                <div class="w-full max-w-md">
                    <label for="revisao_input" class="block text-sm font-medium text-gray-700 mb-2">
                        Revisão <span class="text-gray-500">(opcional)</span>
                    </label>
                    <input type="number"
                           id="revisao_input"
                           x-model="formData.revisao.value"
                           @input="formatRevisao(); saveToSession()"
                           @keypress="validateRevisaoInput($event)"
                           min="1"
                           max="99"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                           :class="{'border-primary': errors.includes('Revisão é obrigatória') || errors.includes('Revisão deve ser um número entre 1 e 99')}"
                           placeholder="Digite um número entre 1 e 99 (opcional)">
                    <p class="text-xs text-gray-500 mt-1">
                        Formato de exibição: <span x-text="formData.revisao.formatted || 'R01'" class="font-mono bg-gray-100 px-1 rounded"></span>
                    </p>
                    <p class="text-xs text-gray-500 mt-1">💡 Campo opcional - deixe em branco se não aplicável</p>
                </div>
            </div>

            <!-- Step 9: Número SEI -->
            <div x-show="currentStep === 9" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">10</span>
                    Número SEI
                </h2>

                <div class="w-full max-w-md">
                    <label for="sei_number_input" class="block text-sm font-medium text-gray-700 mb-2">
                        Número SEI <span class="text-gray-500">(opcional)</span>
                    </label>
                    <input type="text"
                           id="sei_number_input"
                           x-model="formData.seiNumber.numero"
                           @input="validateSeiNumberInput($event); saveToSession()"
                           @keypress="validateNumericInput($event)"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200"
                           :class="{'border-primary': errors.includes('Número SEI é obrigatório') || errors.includes('Número SEI deve conter apenas números')}"
                           placeholder="Digite o número SEI (opcional, apenas números)">
                    <p class="text-xs text-gray-500 mt-1">💡 Campo opcional - deixe em branco se não aplicável</p>
                </div>
            </div>

            <!-- Step 10: Review & Submit -->
            <div x-show="currentStep === 10" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-gray-darker text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">11</span>
                    Revisão Final e Envio
                </h2>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <p class="text-green-800 text-sm">
                        <strong>Revisão Final:</strong> Verifique todas as informações antes de enviar o formulário.
                        Você pode voltar para editar qualquer etapa se necessário.
                    </p>
                </div>

                <!-- Summary of all form data -->
                <div class="space-y-6">
                    <!-- Requisitante Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">1. Requisitante</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Nome:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.requisitante.nome || 'Não informado'"></span>
                                <span x-show="formData.requisitante.nome.trim()"
                                      class="ml-1 text-xs text-gray-500"
                                      x-text="`(${formData.requisitante.nome.trim().length} caracteres)`"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">E-mail:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.requisitante.email || 'Não informado'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Unidade Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">2. Unidade</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.unidade.text || 'Nenhuma unidade selecionada'"></span>
                        </div>
                    </div>

                    <!-- Interessado Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">3. Interessado</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.interessado.text || 'Nenhum interessado selecionado'"></span>
                        </div>
                    </div>

                    <!-- Localização Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">4. Localização</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.localizacao.text || 'Nenhuma localização selecionada'"></span>
                        </div>
                    </div>

                    <!-- Assunto Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">5. Assunto</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.assunto.text || 'Nenhum assunto selecionado'"></span>
                        </div>
                    </div>

                    <!-- Serviço Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">6. Serviço</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Código:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.servico.codigo || 'Não informado'"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Tipo de Serviço:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.servico.tipo_servico || 'Não informado'"></span>
                                <span x-show="formData.servico.tipo_servico.trim()"
                                      class="ml-1 text-xs text-gray-500"
                                      x-text="`(${formData.servico.tipo_servico.trim().length} caracteres)`"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Local Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">7. Local</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.local.text || 'Nenhum local selecionado'"></span>
                        </div>
                    </div>

                    <!-- Disciplina Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">8. Disciplina</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.disciplina.text || 'Nenhuma disciplina selecionada'"></span>
                        </div>
                    </div>

                    <!-- Revisão Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">9. Revisão</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Revisão:</span>
                            <span class="ml-2 font-mono"
                                  :class="formData.revisao.formatted ? 'text-gray-900' : 'text-gray-500 italic'"
                                  x-text="formData.revisao.formatted || 'Não aplicável (campo opcional)'"></span>
                        </div>
                    </div>

                    <!-- Número SEI Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">10. Número SEI</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Número:</span>
                            <span class="ml-2 font-mono"
                                  :class="formData.seiNumber.numero ? 'text-gray-900' : 'text-gray-500 italic'"
                                  x-text="formData.seiNumber.numero || 'Não aplicável (campo opcional)'"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0 mt-6 sm:mt-8">
            <button type="button"
                    @click="previousStep()"
                    x-show="currentStep > 0"
                    class="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-2 bg-gray-dark text-white rounded-md hover:bg-gray-darker focus:outline-none focus:ring-2 focus:ring-gray-dark focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                ← Anterior
            </button>

            <div x-show="currentStep === 0" class="hidden sm:block w-24"></div>

            <button type="button"
                    @click="nextStep()"
                    x-show="currentStep < 10"
                    :disabled="!isStepValid(currentStep)"
                    :class="{
                        'w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 text-sm sm:text-base': true,
                        'bg-secondary text-white hover:bg-blue-700 focus:ring-secondary transform hover:scale-105 shadow-md cursor-pointer': isStepValid(currentStep),
                        'bg-gray-400 text-gray-200 cursor-not-allowed opacity-60': !isStepValid(currentStep)
                    }">
                Próximo →
            </button>

            <button type="button"
                    @click="submitForm()"
                    x-show="currentStep === 10"
                    class="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-2 bg-accent text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 shadow-md text-sm sm:text-base">
                Enviar Formulário
            </button>
        </div>

        <!-- Success Modal -->
        <div x-show="showSuccessModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Cadastro Realizado com Sucesso!</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Suas informações foram enviadas com sucesso. Você receberá uma confirmação por e-mail em breve.
                        </p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button @click="closeSuccessModal()"
                                class="px-4 py-2 bg-secondary text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-secondary transition-all duration-200">
                            Fechar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div x-show="isLoading"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-40"
             style="display: none;">
            <div class="relative top-1/2 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white transform -translate-y-1/2">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-light bg-opacity-20">
                        <svg class="animate-spin h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Processando...</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Aguarde enquanto processamos suas informações.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js Form Logic -->
    <script>
        function seiForm() {
            return {
                currentStep: 0,
                isLoading: false,
                showSuccessModal: false,
                errors: [],

                steps: [
                    { title: 'Requisitante', key: 'requisitante' },
                    { title: 'Unidade', key: 'unidade' },
                    { title: 'Interessado', key: 'interessado' },
                    { title: 'Localização', key: 'localizacao' },
                    { title: 'Assunto', key: 'assunto' },
                    { title: 'Serviço', key: 'servico' },
                    { title: 'Local', key: 'local' },
                    { title: 'Disciplina', key: 'disciplina' },
                    { title: 'Revisão', key: 'revisao' },
                    { title: 'Nro. SEI', key: 'seiNumber' },
                    { title: 'Análise', key: 'review' }
                ],

                formData: {
                    requisitante: {
                        nome: '',
                        email: '',
                        email_confirm: ''
                    },
                    unidade: {
                        id: '',
                        text: '',
                        notApplicable: false
                    },
                    interessado: {
                        id: '',
                        text: '',
                        notApplicable: false
                    },
                    localizacao: {
                        id: '',
                        text: '',
                        notApplicable: false
                    },
                    assunto: {
                        id: '',
                        text: ''
                    },
                    servico: {
                        codigo: '',
                        tipo_servico: ''
                    },
                    local: {
                        id: '',
                        text: '',
                        notApplicable: false
                    },
                    disciplina: {
                        id: '',
                        text: '',
                        notApplicable: false
                    },
                    revisao: {
                        value: '',
                        formatted: ''
                    },
                    seiNumber: {
                        numero: ''
                    }
                },

                init() {
                    // Load data from session storage
                    this.loadFromSession();

                    // Initialize Select2 dropdowns
                    this.$nextTick(() => {
                        this.initializeSelect2();
                    });
                },

                validateStep(stepIndex) {
                    this.errors = [];

                    if (stepIndex === 0) {
                        // Validate Requisitante step
                        const nomeValue = this.formData.requisitante.nome.trim();
                        if (!nomeValue) {
                            this.errors.push('Nome é obrigatório');
                        } else if (nomeValue.length < 3) {
                            this.errors.push('Nome deve ter pelo menos 3 caracteres');
                        } else if (nomeValue.length > 150) {
                            this.errors.push('Nome deve ter no máximo 150 caracteres');
                        }

                        if (!this.formData.requisitante.email.trim()) {
                            this.errors.push('E-mail é obrigatório');
                        }
                        if (!this.formData.requisitante.email_confirm.trim()) {
                            this.errors.push('Confirmação de e-mail é obrigatória');
                        }

                        // Use the dedicated email validation function
                        this.validateEmail();
                    } else if (stepIndex === 1) {
                        // Validate Unidade step
                        if (!this.formData.unidade.id) {
                            this.errors.push('Seleção de unidade é obrigatória');
                        }
                    } else if (stepIndex === 2) {
                        // Validate Interessado step
                        if (!this.formData.interessado.id) {
                            this.errors.push('Seleção de interessado é obrigatória');
                        }
                    } else if (stepIndex === 3) {
                        // Validate Localização step
                        if (!this.formData.localizacao.id) {
                            this.errors.push('Seleção de localização é obrigatória');
                        }
                    } else if (stepIndex === 4) {
                        // Validate Assunto step
                        if (!this.formData.assunto.id) {
                            this.errors.push('Seleção de assunto é obrigatória');
                        }
                    } else if (stepIndex === 5) {
                        // Validate Serviço step - validate codigo if provided
                        if (this.formData.servico.codigo && !this.formData.servico.codigo.match(/^\d+$/)) {
                            this.errors.push('Código do serviço deve conter apenas números');
                        }

                        // Validate servico_tipo if provided (optional field)
                        const servicoTipoValue = this.formData.servico.tipo_servico.trim();
                        if (servicoTipoValue) {
                            if (servicoTipoValue.length < 3) {
                                this.errors.push('Tipo de serviço deve ter pelo menos 3 caracteres');
                            } else if (servicoTipoValue.length > 255) {
                                this.errors.push('Tipo de serviço deve ter no máximo 255 caracteres');
                            }
                        }
                    } else if (stepIndex === 6) {
                        // Validate Local step
                        if (!this.formData.local.id) {
                            this.errors.push('Seleção de local é obrigatória');
                        }
                    } else if (stepIndex === 7) {
                        // Validate Disciplina step
                        if (!this.formData.disciplina.id) {
                            this.errors.push('Seleção de disciplina é obrigatória');
                        }
                    } else if (stepIndex === 8) {
                        // Validate Revisão step - optional field
                        if (this.formData.revisao.value) {
                            const revisaoNum = parseInt(this.formData.revisao.value);
                            if (isNaN(revisaoNum) || revisaoNum < 1 || revisaoNum > 99) {
                                this.errors.push('Revisão deve ser um número entre 1 e 99');
                            }
                        }
                        // No error if field is empty - it's optional
                    } else if (stepIndex === 9) {
                        // Validate Número SEI step - optional field
                        if (this.formData.seiNumber.numero.trim()) {
                            if (!this.formData.seiNumber.numero.match(/^\d+$/)) {
                                this.errors.push('Número SEI deve conter apenas números');
                            }
                        }
                        // No error if field is empty - it's optional
                    }

                    return this.errors.length === 0;
                },

                isStepValid(stepIndex) {
                    if (stepIndex === 0) {
                        const nomeValue = this.formData.requisitante.nome.trim();
                        const emailValid = this.formData.requisitante.email.trim() &&
                                         this.formData.requisitante.email_confirm.trim() &&
                                         this.formData.requisitante.email === this.formData.requisitante.email_confirm;
                        const nomeValid = nomeValue && nomeValue.length >= 3 && nomeValue.length <= 150;

                        return nomeValid && emailValid;
                    } else if (stepIndex === 1) {
                        return !!this.formData.unidade.id;
                    } else if (stepIndex === 2) {
                        return !!this.formData.interessado.id;
                    } else if (stepIndex === 3) {
                        return !!this.formData.localizacao.id;
                    } else if (stepIndex === 4) {
                        return !!this.formData.assunto.id;
                    } else if (stepIndex === 5) {
                        // Serviço fields are optional, but if provided must be valid
                        const codigoValid = !this.formData.servico.codigo || this.formData.servico.codigo.match(/^\d+$/);
                        const servicoTipoValue = this.formData.servico.tipo_servico.trim();
                        const tipoValid = !servicoTipoValue || (servicoTipoValue.length >= 3 && servicoTipoValue.length <= 255);

                        return codigoValid && tipoValid;
                    } else if (stepIndex === 6) {
                        return !!this.formData.local.id;
                    } else if (stepIndex === 7) {
                        return !!this.formData.disciplina.id;
                    } else if (stepIndex === 8) {
                        // Revisão is optional - always valid, but if provided must be valid
                        if (!this.formData.revisao.value) return true; // Empty is valid
                        const revisaoNum = parseInt(this.formData.revisao.value);
                        return !isNaN(revisaoNum) && revisaoNum >= 1 && revisaoNum <= 99;
                    } else if (stepIndex === 9) {
                        // Número SEI is optional - always valid, but if provided must be valid
                        if (!this.formData.seiNumber.numero.trim()) return true; // Empty is valid
                        return this.formData.seiNumber.numero.match(/^\d+$/);
                    }
                    return true;
                },

                nextStep() {
                    if (this.validateStep(this.currentStep)) {
                        if (this.currentStep < 10) { // Now we have 11 steps (0-10)
                            this.currentStep++;
                            this.saveToSession();
                            this.scrollToTop();
                        }
                    }
                },

                previousStep() {
                    if (this.currentStep > 0) {
                        this.currentStep--;
                        this.errors = []; // Clear errors when going back
                        this.saveToSession();
                        this.scrollToTop();
                    }
                },

                navigateToStep(stepIndex) {
                    // Prevent navigation to future steps (beyond current + 1)
                    if (stepIndex > this.currentStep) {
                        // Show visual feedback that this step is not accessible
                        return false;
                    }

                    // Allow navigation to completed steps or current step
                    if (stepIndex <= this.currentStep) {
                        this.currentStep = stepIndex;
                        this.saveToSession();
                        this.scrollToTop();
                        return true;
                    }

                    // Allow navigation to next step only if current step is valid
                    if (stepIndex === this.currentStep + 1 && this.isStepValid(this.currentStep)) {
                        if (this.validateStep(this.currentStep)) {
                            this.currentStep = stepIndex;
                            this.saveToSession();
                            this.scrollToTop();
                            return true;
                        }
                    }

                    return false;
                },

                scrollToTop() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                },

                saveToSession() {
                    // Save current step and form data to session storage
                    sessionStorage.setItem('seiFormStep', this.currentStep);
                    sessionStorage.setItem('seiFormData', JSON.stringify(this.formData));
                },

                loadFromSession() {
                    // Load step and form data from session storage
                    const savedStep = sessionStorage.getItem('seiFormStep');
                    const savedData = sessionStorage.getItem('seiFormData');

                    if (savedStep !== null) {
                        this.currentStep = parseInt(savedStep);
                    }

                    if (savedData) {
                        try {
                            const parsedData = JSON.parse(savedData);
                            this.formData = { ...this.formData, ...parsedData };

                            // Restore Select2 values after initialization
                            this.$nextTick(() => {
                                this.restoreSelect2Values();
                            });
                        } catch (e) {
                            console.error('Error parsing saved form data:', e);
                        }
                    }
                },

                restoreSelect2Values() {
                    // Restore Select2 selections from saved data
                    if (this.formData.unidade.id && this.formData.unidade.text) {
                        const option = new Option(this.formData.unidade.text, this.formData.unidade.id, true, true);
                        $('#unidade_select').append(option).trigger('change');
                    }

                    if (this.formData.interessado.id && this.formData.interessado.text) {
                        const option = new Option(this.formData.interessado.text, this.formData.interessado.id, true, true);
                        $('#interessado_select').append(option).trigger('change');
                    }

                    if (this.formData.localizacao.id && this.formData.localizacao.text) {
                        const option = new Option(this.formData.localizacao.text, this.formData.localizacao.id, true, true);
                        $('#localizacao_select').append(option).trigger('change');
                    }

                    if (this.formData.assunto.id && this.formData.assunto.text) {
                        const option = new Option(this.formData.assunto.text, this.formData.assunto.id, true, true);
                        $('#assunto_select').append(option).trigger('change');
                    }

                    if (this.formData.local.id && this.formData.local.text) {
                        const option = new Option(this.formData.local.text, this.formData.local.id, true, true);
                        $('#local_select').append(option).trigger('change');
                    }

                    if (this.formData.disciplina.id && this.formData.disciplina.text) {
                        const option = new Option(this.formData.disciplina.text, this.formData.disciplina.id, true, true);
                        $('#disciplina_select').append(option).trigger('change');
                    }
                },

                clearSession() {
                    sessionStorage.removeItem('seiFormStep');
                    sessionStorage.removeItem('seiFormData');
                },

                async submitForm() {
                    if (!this.validateStep(this.currentStep)) {
                        return;
                    }

                    this.isLoading = true;

                    try {
                        // Prepare submission data (trim text fields before submission)
                        const submissionData = {
                            requisitante: {
                                nome: this.formData.requisitante.nome.trim(),
                                email: this.formData.requisitante.email.trim()
                            },
                            unidade_id: this.formData.unidade.notApplicable ? 'NA' : (this.formData.unidade.id || null),
                            interessado_id: this.formData.interessado.notApplicable ? 'NA' : (this.formData.interessado.id || null),
                            localizacao_id: this.formData.localizacao.notApplicable ? 'NA' : (this.formData.localizacao.id || null),
                            assunto_id: this.formData.assunto.id || null,
                            servico_codigo: this.formData.servico.codigo || '',
                            servico_tipo: this.formData.servico.tipo_servico.trim(),
                            local_id: this.formData.local.notApplicable ? 'NA' : (this.formData.local.id || null),
                            disciplina_id: this.formData.disciplina.notApplicable ? 'NA' : (this.formData.disciplina.id || null),
                            doc_revisao: this.formData.revisao.formatted,
                            doc_sei_num: this.formData.seiNumber.numero
                        };

                        const response = await fetch('/sei/api/submit/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': this.getCSRFToken()
                            },
                            body: JSON.stringify(submissionData)
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            // Validate that we have the required protocolo_id
                            if (!result.protocolo_id) {
                                throw new Error('Protocolo criado com sucesso, mas ID não foi retornado. Recarregue a página.');
                            }

                            // Validate protocolo_id format (should be a UUID)
                            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                            if (!uuidRegex.test(result.protocolo_id)) {
                                throw new Error('ID do protocolo inválido. Recarregue a página.');
                            }

                            // Clear form data from session before redirecting
                            this.clearSession();

                            try {
                                // Construct and validate the success URL
                                const successUrl = `/sei/success/${encodeURIComponent(result.protocolo_id)}/`;
                                console.log('Redirecting to:', successUrl);

                                // Show redirection message
                                this.isLoading = true;
                                this.errors = ['Protocolo criado com sucesso! Redirecionando...'];

                                // Perform the redirection
                                window.location.href = successUrl;

                                // Fallback: if redirection doesn't happen within 5 seconds, show manual link
                                setTimeout(() => {
                                    // Check if we're still on the form page (redirection failed)
                                    if (window.location.pathname.includes('/sei/') && !window.location.pathname.includes('/success/')) {
                                        this.isLoading = false;
                                        const userConfirm = confirm(
                                            `Protocolo criado com sucesso!\n` +
                                            `Código: ${result.doc_cod}\n\n` +
                                            `A página não foi redirecionada automaticamente.\n` +
                                            `Clique em OK para ir para a página de confirmação.`
                                        );
                                        if (userConfirm) {
                                            window.location.href = successUrl;
                                        }
                                    }
                                }, 5000);

                            } catch (redirectError) {
                                console.error('Redirection failed:', redirectError);
                                // Fallback: show success message with manual link
                                const userConfirm = confirm(
                                    `Protocolo criado com sucesso!\n` +
                                    `Código: ${result.doc_cod}\n\n` +
                                    `Clique em OK para ver os detalhes do protocolo.`
                                );
                                if (userConfirm) {
                                    window.open(`/sei/success/${result.protocolo_id}/`, '_blank');
                                }
                            }
                        } else {
                            throw new Error(result.message || 'Erro ao enviar formulário');
                        }
                    } catch (error) {
                        console.error('Error submitting form:', error);
                        this.errors = [error.message || 'Erro ao enviar formulário. Tente novamente.'];
                    } finally {
                        this.isLoading = false;
                    }
                },

                closeSuccessModal() {
                    this.showSuccessModal = false;
                    // Reset form
                    this.currentStep = 0;
                    this.formData = {
                        requisitante: { nome: '', email: '', email_confirm: '' },
                        unidade: { id: '', text: '', notApplicable: false },
                        interessado: { id: '', text: '', notApplicable: false },
                        localizacao: { id: '', text: '', notApplicable: false },
                        assunto: { id: '', text: '' },
                        servico: { codigo: '', tipo_servico: '' },
                        local: { id: '', text: '', notApplicable: false },
                        disciplina: { id: '', text: '', notApplicable: false },
                        revisao: { value: '', formatted: '' },
                        seiNumber: { numero: '' }
                    };
                    // Clear Select2 selections
                    $('#unidade_select').val(null).trigger('change');
                    $('#interessado_select').val(null).trigger('change');
                    $('#localizacao_select').val(null).trigger('change');
                    $('#assunto_select').val(null).trigger('change');
                    $('#local_select').val(null).trigger('change');
                    $('#disciplina_select').val(null).trigger('change');
                },

                getCSRFToken() {
                    // Get CSRF token from Django
                    const cookies = document.cookie.split(';');
                    for (let cookie of cookies) {
                        const [name, value] = cookie.trim().split('=');
                        if (name === 'csrftoken') {
                            return value;
                        }
                    }
                    return '';
                },

                handleNotApplicableChange(fieldName, isChecked) {
                    if (isChecked) {
                        // Clear the Select2 field and disable it
                        $(`#${fieldName}_select`).val(null).trigger('change');
                        this.formData[fieldName].id = '';
                        this.formData[fieldName].text = '';

                        // Set a special flag to indicate "Not Applicable" was selected
                        this.formData[fieldName].notApplicable = true;
                    } else {
                        // Re-enable the field
                        this.formData[fieldName].notApplicable = false;
                    }

                    // Save to session
                    this.saveToSession();
                },

                initializeSelect2() {
                    const self = this;

                    // Basic Select2 configuration
                    const commonConfig = {
                        width: '100%'
                    };

                    // Initialize Select2 for Unidade
                    $('#unidade_select').select2({
                        ...commonConfig,
                        placeholder: 'Selecione uma unidade ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/unidade/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.unidade.id = e.params.data.id;
                        self.formData.unidade.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.unidade.id = '';
                        self.formData.unidade.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Interessado
                    $('#interessado_select').select2({
                        ...commonConfig,
                        placeholder: 'Selecione um interessado ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/interessado/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.interessado.id = e.params.data.id;
                        self.formData.interessado.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.interessado.id = '';
                        self.formData.interessado.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Localização
                    $('#localizacao_select').select2({
                        ...commonConfig,
                        placeholder: 'Selecione uma localização ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/localizacao/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.localizacao.id = e.params.data.id;
                        self.formData.localizacao.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.localizacao.id = '';
                        self.formData.localizacao.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Assunto
                    $('#assunto_select').select2({
                        ...commonConfig,
                        placeholder: 'Selecione um assunto ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/assunto/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.assunto.id = e.params.data.id;
                        self.formData.assunto.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.assunto.id = '';
                        self.formData.assunto.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Local
                    $('#local_select').select2({
                        ...commonConfig,
                        placeholder: 'Selecione um local ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/local/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.local.id = e.params.data.id;
                        self.formData.local.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.local.id = '';
                        self.formData.local.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Disciplina
                    $('#disciplina_select').select2({
                        ...commonConfig,
                        placeholder: 'Selecione uma disciplina ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/disciplina/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.disciplina.id = e.params.data.id;
                        self.formData.disciplina.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.disciplina.id = '';
                        self.formData.disciplina.text = '';
                        self.saveToSession();
                    });


                },

                validateNumericInput(event) {
                    // Prevent non-numeric characters from being typed
                    const char = String.fromCharCode(event.which);
                    if (!/[0-9]/.test(char)) {
                        event.preventDefault();
                    }
                },

                validateServicoCodigoInput(event) {
                    // Remove any non-numeric characters from the input
                    const value = event.target.value;
                    const numericValue = value.replace(/[^0-9]/g, '');

                    if (value !== numericValue) {
                        this.formData.servico.codigo = numericValue;
                        event.target.value = numericValue;
                    }

                    // Clear any existing validation errors for this field
                    this.errors = this.errors.filter(error =>
                        error !== 'Código do serviço deve conter apenas números'
                    );
                },

                formatRevisao() {
                    // Format revisão as R + zero-padded 2-digit number
                    const value = parseInt(this.formData.revisao.value);
                    if (!isNaN(value) && value >= 1 && value <= 99) {
                        this.formData.revisao.formatted = 'R' + value.toString().padStart(2, '0');
                    } else {
                        this.formData.revisao.formatted = '';
                    }

                    // Clear validation errors for this field
                    this.errors = this.errors.filter(error =>
                        !error.includes('Revisão')
                    );
                },

                validateRevisaoInput(event) {
                    // Only allow numeric input for revisão
                    const char = String.fromCharCode(event.which);
                    if (!/[0-9]/.test(char)) {
                        event.preventDefault();
                    }
                },

                validateSeiNumberInput(event) {
                    // Remove any non-numeric characters from SEI number input
                    const value = event.target.value;
                    const numericValue = value.replace(/[^0-9]/g, '');

                    if (value !== numericValue) {
                        this.formData.seiNumber.numero = numericValue;
                        event.target.value = numericValue;
                    }

                    // Clear validation errors for this field
                    this.errors = this.errors.filter(error =>
                        !error.includes('Número SEI')
                    );
                },

                formatNomeInput(event) {
                    // Convert to uppercase but preserve all spaces (including trailing ones during typing)
                    // This allows users to type "João Silva" naturally without spaces being removed
                    let value = event.target.value.toUpperCase();

                    // Update the form data with the current value (don't trim during typing)
                    this.formData.requisitante.nome = value;
                    // Don't modify the input value - let user type naturally

                    // Clear existing nome validation errors
                    this.errors = this.errors.filter(error =>
                        !error.includes('Nome') && !error.includes('nome')
                    );

                    // For validation, use trimmed value but don't modify the input
                    let trimmedValue = value.trim();

                    // Real-time validation
                    if (trimmedValue) {
                        if (trimmedValue.length < 3) {
                            this.errors.push('Nome deve ter pelo menos 3 caracteres');
                        } else if (trimmedValue.length > 150) {
                            this.errors.push('Nome deve ter no máximo 150 caracteres');
                        }
                    } else if (value.length > 0) {
                        // If there's content but it's all spaces
                        this.errors.push('Nome é obrigatório');
                    }

                    this.saveToSession();
                },

                formatEmailInput(event) {
                    // Strip whitespace and convert to lowercase for email
                    let value = event.target.value.trim().toLowerCase();
                    this.formData.requisitante.email = value;
                    event.target.value = value;
                    this.validateEmail();
                    this.saveToSession();
                },

                formatEmailConfirmInput(event) {
                    // Strip whitespace and convert to lowercase for email confirmation
                    let value = event.target.value.trim().toLowerCase();
                    this.formData.requisitante.email_confirm = value;
                    event.target.value = value;
                    this.validateEmail();
                    this.saveToSession();
                },

                formatServicoTipoInput(event) {
                    // Convert to uppercase but preserve all spaces (including trailing ones during typing)
                    // This allows users to type "Análise Técnica" naturally without spaces being removed
                    let value = event.target.value.toUpperCase();

                    // Update the form data with the current value (don't trim during typing)
                    this.formData.servico.tipo_servico = value;
                    // Don't modify the input value - let user type naturally

                    // Clear existing servico tipo validation errors
                    this.errors = this.errors.filter(error =>
                        !error.includes('Tipo de serviço') && !error.includes('tipo de serviço')
                    );

                    // For validation, use trimmed value but don't modify the input
                    let trimmedValue = value.trim();

                    // Real-time validation (only if field has content since it's optional)
                    if (trimmedValue) {
                        if (trimmedValue.length < 3) {
                            this.errors.push('Tipo de serviço deve ter pelo menos 3 caracteres');
                        } else if (trimmedValue.length > 255) {
                            this.errors.push('Tipo de serviço deve ter no máximo 255 caracteres');
                        }
                    }
                    // Note: Empty field is valid for servico_tipo (optional field)

                    this.saveToSession();
                },

                validateEmail() {
                    // Clear existing email validation errors
                    this.errors = this.errors.filter(error =>
                        !error.includes('E-mail') && !error.includes('email')
                    );

                    // Validate email format
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (this.formData.requisitante.email && !emailRegex.test(this.formData.requisitante.email)) {
                        this.errors.push('E-mail inválido');
                    }

                    // Validate email confirmation match
                    if (this.formData.requisitante.email && this.formData.requisitante.email_confirm) {
                        if (this.formData.requisitante.email !== this.formData.requisitante.email_confirm) {
                            this.errors.push('E-mails não coincidem');
                        }
                    }
                },

                // Debug function to test redirection (can be called from browser console)
                testRedirection(protocoloId) {
                    if (!protocoloId) {
                        console.error('Please provide a protocolo ID');
                        return;
                    }

                    const successUrl = `/sei/success/${protocoloId}/`;
                    console.log('Testing redirection to:', successUrl);
                    window.location.href = successUrl;
                }
            }
        }
    </script>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-darkest text-white mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-light">
                    © 2025 ARTESP - Sistema GERPRO - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>
</body>
</html>