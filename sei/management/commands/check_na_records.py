"""
Management command to check if "Not Applicable" records exist for all required models.
"""

from django.core.management.base import BaseCommand
from django.core.management.color import make_style
from sei.models import Unidade, Interessado, Localizacao, Local, Disciplina


class Command(BaseCommand):
    help = 'Check if "Not Applicable" records exist for all required models'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.style = make_style()

    def handle(self, *args, **options):
        """Main command handler"""
        self.stdout.write(self.style.SUCCESS('Checking "Not Applicable" records...'))
        self.stdout.write('')

        # Models that should have NA records
        models_to_check = [
            ('Unidade', Unidade),
            ('Interessado', Interessado),
            ('Localizacao', Localizacao),
            ('Local', Local),
            ('Disciplina', Disciplina),
        ]

        all_good = True

        for model_name, model_class in models_to_check:
            try:
                na_record = model_class.objects.get(codigo='NA')
                self.stdout.write(
                    self.style.SUCCESS(f'✓ {model_name}: Found NA record (ID: {na_record.id})')
                )
                
                # Show the record details
                if hasattr(na_record, 'unidade'):
                    detail = na_record.unidade
                elif hasattr(na_record, 'concessao'):
                    detail = na_record.concessao
                elif hasattr(na_record, 'localizacao'):
                    detail = na_record.localizacao
                elif hasattr(na_record, 'local'):
                    detail = na_record.local
                elif hasattr(na_record, 'disciplina'):
                    detail = na_record.disciplina
                else:
                    detail = 'N/A'
                
                self.stdout.write(f'  Details: {detail}')
                
            except model_class.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'✗ {model_name}: NA record NOT FOUND')
                )
                self.stdout.write(
                    self.style.WARNING(f'  You need to create a {model_name} record with codigo="NA"')
                )
                all_good = False
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ {model_name}: Error checking - {str(e)}')
                )
                all_good = False

        self.stdout.write('')
        if all_good:
            self.stdout.write(
                self.style.SUCCESS('✓ All required "Not Applicable" records are present!')
            )
        else:
            self.stdout.write(
                self.style.ERROR('✗ Some "Not Applicable" records are missing!')
            )
            self.stdout.write('')
            self.stdout.write('To fix missing records, you can:')
            self.stdout.write('1. Load fixtures: python manage.py loaddata sei/fixtures/*.json')
            self.stdout.write('2. Or create records manually in Django admin')
