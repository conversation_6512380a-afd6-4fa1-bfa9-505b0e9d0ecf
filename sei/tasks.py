import logging
import tempfile
import os
from io import BytesIO
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from celery import shared_task
from xhtml2pdf import pisa
from .models import Protocolo
from .utils import get_pdf_context, generate_pdf_filename

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_protocolo_email_notification(self, protocolo_id):
    """
    Send email notification with PDF attachment when a SEI form is successfully submitted.

    This task sends a professional email to the requisitante containing:
    - Protocol information formatted for email
    - PDF attachment with the complete protocol document
    - Proper error handling and retry logic

    Args:
        protocolo_id (str): UUID of the Protocolo instance

    Returns:
        dict: Result dictionary with success status and details
    """
    # Early debugging - task started
    logger.info(f"🚀 CELERY TASK STARTED: send_protocolo_email_notification for protocolo_id={protocolo_id}")
    print(f"🚀 CELERY TASK STARTED: send_protocolo_email_notification for protocolo_id={protocolo_id}")

    try:
        # Get the protocolo instance with related objects
        logger.info(f"📋 STEP 1: Fetching protocolo with ID {protocolo_id}")
        print(f"📋 STEP 1: Fetching protocolo with ID {protocolo_id}")

        protocolo = _get_protocolo_with_relations(protocolo_id)
        if not protocolo:
            logger.error(f"Protocolo with ID {protocolo_id} not found")
            return {'success': False, 'error': 'Protocolo not found'}

        logger.info(f"✅ STEP 1 COMPLETE: Protocolo found - {protocolo.doc_cod}")
        print(f"✅ STEP 1 COMPLETE: Protocolo found - {protocolo.doc_cod}")

        # Validate email recipient
        logger.info(f"📧 STEP 2: Validating email recipient")
        print(f"📧 STEP 2: Validating email recipient")

        if not protocolo.usuario or not protocolo.usuario.email:
            logger.error(f"No valid email address for protocolo {protocolo_id}")
            return {'success': False, 'error': 'No valid email address'}

        logger.info(f"✅ STEP 2 COMPLETE: Email recipient valid - {protocolo.usuario.email}")
        print(f"✅ STEP 2 COMPLETE: Email recipient valid - {protocolo.usuario.email}")

        # Generate PDF content
        logger.info(f"📄 STEP 3: Generating PDF content")
        print(f"📄 STEP 3: Generating PDF content")

        pdf_content = _generate_pdf_content(protocolo)
        if not pdf_content:
            logger.error(f"Failed to generate PDF for protocolo {protocolo_id}")
            return {'success': False, 'error': 'PDF generation failed'}

        logger.info(f"✅ STEP 3 COMPLETE: PDF generated - {len(pdf_content)} bytes")
        print(f"✅ STEP 3 COMPLETE: PDF generated - {len(pdf_content)} bytes")

        # Send email with PDF attachment
        logger.info(f"📤 STEP 4: Calling _send_email_with_pdf function")
        print(f"📤 STEP 4: Calling _send_email_with_pdf function")

        email_result = _send_email_with_pdf(protocolo, pdf_content)

        logger.info(f"📤 STEP 4 RESULT: _send_email_with_pdf returned - {email_result}")
        print(f"📤 STEP 4 RESULT: _send_email_with_pdf returned - {email_result}")

        if email_result['success']:
            logger.info(f"Email notification sent successfully for protocolo {protocolo_id}")
            return {
                'success': True,
                'protocolo_id': protocolo_id,
                'email': protocolo.usuario.email,
                'sent_at': timezone.now().isoformat()
            }
        else:
            # Retry on email sending failure
            logger.warning(f"Email sending failed for protocolo {protocolo_id}: {email_result['error']}")
            raise Exception(f"Email sending failed: {email_result['error']}")

    except Exception as exc:
        logger.error(f"❌ EXCEPTION CAUGHT in email notification task for protocolo {protocolo_id}")
        logger.error(f"❌ Exception type: {type(exc).__name__}")
        logger.error(f"❌ Exception message: {str(exc)}")
        print(f"❌ EXCEPTION CAUGHT in email notification task for protocolo {protocolo_id}")
        print(f"❌ Exception type: {type(exc).__name__}")
        print(f"❌ Exception message: {str(exc)}")

        # Import traceback to get full stack trace
        import traceback
        full_traceback = traceback.format_exc()
        logger.error(f"❌ Full traceback:\n{full_traceback}")
        print(f"❌ Full traceback:\n{full_traceback}")

        # Retry the task with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = 60 * (2 ** self.request.retries)  # Exponential backoff
            logger.info(f"Retrying email notification task in {retry_delay} seconds (attempt {self.request.retries + 1})")
            raise self.retry(countdown=retry_delay, exc=exc)

        # Max retries reached
        logger.error(f"Max retries reached for email notification task for protocolo {protocolo_id}")
        return {
            'success': False,
            'error': str(exc),
            'protocolo_id': protocolo_id,
            'max_retries_reached': True
        }


def _get_protocolo_with_relations(protocolo_id):
    """
    Get Protocolo instance with all related objects loaded.

    Args:
        protocolo_id (str): UUID of the Protocolo instance

    Returns:
        Protocolo: Protocolo instance with related objects or None if not found
    """
    try:
        return Protocolo.objects.select_related(
            'usuario', 'unidade', 'interessado', 'localizacao',
            'assunto', 'local', 'disciplina'
        ).get(id=protocolo_id)
    except Protocolo.DoesNotExist:
        return None
    except Exception as e:
        logger.error(f"Error fetching protocolo {protocolo_id}: {str(e)}")
        return None


def _generate_pdf_content(protocolo):
    """
    Generate PDF content for email attachment.

    Args:
        protocolo: Protocolo instance with related objects loaded

    Returns:
        bytes: PDF content as bytes or None if generation fails
    """
    try:
        # Prepare context for PDF template
        context = get_pdf_context(protocolo)

        # Render HTML template
        html_content = render_to_string('sei/pdf.html', context)

        # Generate PDF using xhtml2pdf
        pdf_buffer = BytesIO()
        pisa_status = pisa.CreatePDF(
            html_content.encode('utf-8'),
            dest=pdf_buffer,
            encoding='utf-8'
        )

        if pisa_status.err:
            logger.error(f"PDF generation error for protocolo {protocolo.id}: {pisa_status.err}")
            return None

        pdf_content = pdf_buffer.getvalue()
        pdf_buffer.close()

        return pdf_content

    except Exception as e:
        logger.error(f"Error generating PDF for protocolo {protocolo.id}: {str(e)}")
        return None


def _send_email_with_pdf(protocolo, pdf_content):
    """
    Send email with PDF attachment.

    Args:
        protocolo: Protocolo instance
        pdf_content (bytes): PDF content as bytes

    Returns:
        dict: Result dictionary with success status and details
    """
    logger.info(f"🔧 ENTERING _send_email_with_pdf for protocolo {protocolo.id}")
    print(f"🔧 ENTERING _send_email_with_pdf for protocolo {protocolo.id}")

    try:
        # Prepare email context
        logger.info(f"📝 Preparing email context")
        print(f"📝 Preparing email context")
        email_context = _prepare_email_context(protocolo)

        # Generate email subject
        subject = f"[ ARTESP-GERPRO ] Protocolo Criado com Sucesso - {protocolo.doc_cod}"
        logger.info(f"📝 Email subject: {subject}")
        print(f"📝 Email subject: {subject}")

        # Render email templates
        logger.info(f"📝 Rendering email templates")
        print(f"📝 Rendering email templates")
        html_message = render_to_string('sei/email/protocolo_notification.html', email_context)
        text_message = render_to_string('sei/email/protocolo_notification.txt', email_context)

        # Create email message with HTML support
        logger.info(f"📧 Creating EmailMultiAlternatives object")
        print(f"📧 Creating EmailMultiAlternatives object")
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=getattr(settings, 'MAIL_FROM', '<EMAIL>'),
            to=[protocolo.usuario.email],
            reply_to=[getattr(settings, 'MAIL_FROM', '<EMAIL>')]
        )
        logger.info(f"✅ EmailMultiAlternatives object created successfully")
        print(f"✅ EmailMultiAlternatives object created successfully")

        # Add HTML alternative
        logger.info(f"📎 Adding HTML alternative")
        print(f"📎 Adding HTML alternative")
        email.attach_alternative(html_message, "text/html")

        # Add PDF attachment
        logger.info(f"📎 Adding PDF attachment")
        print(f"📎 Adding PDF attachment")
        pdf_filename = generate_pdf_filename(protocolo)
        email.attach(f"{pdf_filename}.pdf", pdf_content, 'application/pdf')
        logger.info(f"✅ PDF attachment added: {pdf_filename}.pdf")
        print(f"✅ PDF attachment added: {pdf_filename}.pdf")

        # Debug output before sending email
        debug_info = [
            "=" * 80,
            "EMAIL DEBUG OUTPUT - PROTOCOLO NOTIFICATION",
            "=" * 80,
            "📧 EMAIL DATA:",
            f"   Subject: {subject}",
            f"   From: {getattr(settings, 'MAIL_FROM', '<EMAIL>')}",
            f"   To: {protocolo.usuario.email}",
            f"   Reply-to: {getattr(settings, 'MAIL_FROM', '<EMAIL>')}",
            f"   PDF Attachment: {pdf_filename}.pdf ({len(pdf_content)} bytes)",
            "",
            "🔧 SMTP CONFIGURATION:",
            f"   Server: {getattr(settings, 'EMAIL_HOST', 'Not configured')}",
            f"   Port: {getattr(settings, 'EMAIL_PORT', 'Not configured')}",
            f"   TLS Enabled: {getattr(settings, 'EMAIL_USE_TLS', False)}",
            f"   SSL Enabled: {getattr(settings, 'EMAIL_USE_SSL', False)}",
            f"   Backend: {getattr(settings, 'EMAIL_BACKEND', 'Not configured')}",
            f"   Host User: {getattr(settings, 'EMAIL_HOST_USER', 'Not configured')}",
            f"   Host Password: {'***CONFIGURED***' if getattr(settings, 'EMAIL_HOST_PASSWORD', None) else 'NOT CONFIGURED'}",
            f"   Authentication: {'Required' if getattr(settings, 'EMAIL_HOST_USER', None) and getattr(settings, 'EMAIL_HOST_PASSWORD', None) else 'Not Required (Open SMTP)'}",
            "=" * 80
        ]

        # Log debug info (will appear in Django logs)
        for line in debug_info:
            logger.info(line)

        # Also print to console (will appear in Celery worker logs)
        for line in debug_info:
            print(line)

        # Send email
        try:
            logger.info("🚀 ATTEMPTING TO SEND EMAIL...")
            print("🚀 ATTEMPTING TO SEND EMAIL...")

            # Additional debug: Check if Django will attempt authentication
            host_user = getattr(settings, 'EMAIL_HOST_USER', None)
            host_password = getattr(settings, 'EMAIL_HOST_PASSWORD', None)
            auth_debug = f"🔐 AUTHENTICATION DEBUG: user='{host_user}', password={'SET' if host_password else 'NOT SET'}"
            logger.info(auth_debug)
            print(auth_debug)

            email.send()
            logger.info("✅ EMAIL SENT SUCCESSFULLY!")
            print("✅ EMAIL SENT SUCCESSFULLY!")
        except Exception as send_error:
            error_msg = f"❌ EMAIL SEND FAILED: {str(send_error)}"
            logger.error(error_msg)
            print(error_msg)
            raise  # Re-raise the exception to maintain original error handling

        return {
            'success': True,
            'email': protocolo.usuario.email,
            'subject': subject
        }

    except Exception as e:
        logger.error(f"Error sending email for protocolo {protocolo.id}: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_protocolo_update_notification(self, protocolo_id, old_value, new_value):
    """
    Send email notification when a protocol's doc_sei_num field is updated.

    This task sends a professional email to the requisitante containing:
    - Information about what was changed (old vs new value)
    - Complete protocol information
    - Update timestamp and security information

    Args:
        protocolo_id (str): UUID of the Protocolo instance
        old_value (str): Previous value of doc_sei_num field
        new_value (str): New value of doc_sei_num field

    Returns:
        dict: Result dictionary with success status and details
    """
    try:
        # Get the protocolo instance with related objects
        protocolo = _get_protocolo_with_relations(protocolo_id)
        if not protocolo:
            logger.error(f"Protocolo with ID {protocolo_id} not found for update notification")
            return {'success': False, 'error': 'Protocolo not found'}

        # Validate email recipient
        if not protocolo.usuario or not protocolo.usuario.email:
            logger.error(f"No valid email address for protocolo {protocolo_id} update notification")
            return {'success': False, 'error': 'No valid email address'}

        # Send update notification email
        email_result = _send_update_email(protocolo, old_value, new_value)

        if email_result['success']:
            logger.info(f"Update notification sent successfully for protocolo {protocolo_id}")
            return {
                'success': True,
                'protocolo_id': protocolo_id,
                'email': protocolo.usuario.email,
                'old_value': old_value,
                'new_value': new_value,
                'sent_at': timezone.now().isoformat()
            }
        else:
            # Retry on email sending failure
            logger.warning(f"Update notification email failed for protocolo {protocolo_id}: {email_result['error']}")
            raise Exception(f"Email sending failed: {email_result['error']}")

    except Exception as exc:
        logger.error(f"Error in update notification task for protocolo {protocolo_id}: {str(exc)}")

        # Retry the task with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = 60 * (2 ** self.request.retries)  # Exponential backoff
            logger.info(f"Retrying update notification task in {retry_delay} seconds (attempt {self.request.retries + 1})")
            raise self.retry(countdown=retry_delay, exc=exc)

        # Max retries reached
        logger.error(f"Max retries reached for update notification task for protocolo {protocolo_id}")
        return {
            'success': False,
            'error': str(exc),
            'protocolo_id': protocolo_id,
            'max_retries_reached': True
        }


def _send_update_email(protocolo, old_value, new_value):
    """
    Send update notification email with change details and PDF attachment.

    Args:
        protocolo: Protocolo instance
        old_value (str): Previous value of doc_sei_num
        new_value (str): New value of doc_sei_num

    Returns:
        dict: Result dictionary with success status and details
    """
    try:
        # Generate PDF content first
        logger.info(f"📄 Generating PDF content for update notification - protocolo {protocolo.id}")
        print(f"📄 Generating PDF content for update notification - protocolo {protocolo.id}")

        pdf_content = _generate_pdf_content(protocolo)
        if not pdf_content:
            logger.error(f"Failed to generate PDF for update notification - protocolo {protocolo.id}")
            # Continue without PDF attachment rather than failing completely
            pdf_content = None

        # Prepare email context with update information
        email_context = _prepare_update_email_context(protocolo, old_value, new_value)

        # Generate email subject
        subject = f"[ ARTESP-GERPRO ] Protocolo Atualizado - {protocolo.doc_cod}"

        # Render email templates
        html_message = render_to_string('sei/email/protocolo_update_notification.html', email_context)
        text_message = render_to_string('sei/email/protocolo_update_notification.txt', email_context)

        # Create email message with HTML support
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_message,
            from_email=getattr(settings, 'MAIL_FROM', '<EMAIL>'),
            to=[protocolo.usuario.email],
            reply_to=[getattr(settings, 'MAIL_FROM', '<EMAIL>')]
        )

        # Add HTML alternative
        email.attach_alternative(html_message, "text/html")

        # Add PDF attachment if generation was successful
        if pdf_content:
            logger.info(f"📎 Adding PDF attachment to update notification")
            print(f"📎 Adding PDF attachment to update notification")
            pdf_filename = generate_pdf_filename(protocolo)
            email.attach(f"{pdf_filename}.pdf", pdf_content, 'application/pdf')
            logger.info(f"✅ PDF attachment added: {pdf_filename}.pdf ({len(pdf_content)} bytes)")
            print(f"✅ PDF attachment added: {pdf_filename}.pdf ({len(pdf_content)} bytes)")
        else:
            logger.warning(f"⚠️ Sending update notification without PDF attachment due to generation failure")
            print(f"⚠️ Sending update notification without PDF attachment due to generation failure")

        # Debug output before sending email
        debug_info = [
            "=" * 80,
            "EMAIL DEBUG OUTPUT - PROTOCOLO UPDATE NOTIFICATION",
            "=" * 80,
            "📧 EMAIL DATA:",
            f"   Subject: {subject}",
            f"   From: {getattr(settings, 'MAIL_FROM', '<EMAIL>')}",
            f"   To: {protocolo.usuario.email}",
            f"   Reply-to: {getattr(settings, 'MAIL_FROM', '<EMAIL>')}",
            f"   Update Info: {old_value} → {new_value}",
            f"   PDF Attachment: {'Yes' if pdf_content else 'No'} ({len(pdf_content) if pdf_content else 0} bytes)",
            "",
            "📝 CONTEXT DATA:",
            f"   Protocolo ID: {protocolo.id}",
            f"   Doc Code: {protocolo.doc_cod}",
            f"   User: {protocolo.usuario.nome} ({protocolo.usuario.email})",
            f"   Old Value: '{old_value}'",
            f"   New Value: '{new_value}'",
            "",
            "🔧 SMTP CONFIGURATION:",
            f"   Server: {getattr(settings, 'EMAIL_HOST', 'Not configured')}",
            f"   Port: {getattr(settings, 'EMAIL_PORT', 'Not configured')}",
            f"   TLS Enabled: {getattr(settings, 'EMAIL_USE_TLS', False)}",
            f"   SSL Enabled: {getattr(settings, 'EMAIL_USE_SSL', False)}",
            f"   Backend: {getattr(settings, 'EMAIL_BACKEND', 'Not configured')}",
            f"   Host User: {getattr(settings, 'EMAIL_HOST_USER', 'Not configured')}",
            f"   Host Password: {'***CONFIGURED***' if getattr(settings, 'EMAIL_HOST_PASSWORD', None) else 'NOT CONFIGURED'}",
            f"   Authentication: {'Required' if getattr(settings, 'EMAIL_HOST_USER', None) and getattr(settings, 'EMAIL_HOST_PASSWORD', None) else 'Not Required (Open SMTP)'}",
            "=" * 80
        ]

        # Log debug info (will appear in Django logs)
        for line in debug_info:
            logger.info(line)

        # Also print to console (will appear in Celery worker logs)
        for line in debug_info:
            print(line)

        # Send email
        try:
            logger.info("🚀 ATTEMPTING TO SEND EMAIL...")
            print("🚀 ATTEMPTING TO SEND EMAIL...")

            # Additional debug: Check if Django will attempt authentication
            host_user = getattr(settings, 'EMAIL_HOST_USER', None)
            host_password = getattr(settings, 'EMAIL_HOST_PASSWORD', None)
            auth_debug = f"🔐 AUTHENTICATION DEBUG: user='{host_user}', password={'SET' if host_password else 'NOT SET'}"
            logger.info(auth_debug)
            print(auth_debug)

            email.send()
            logger.info("✅ EMAIL SENT SUCCESSFULLY!")
            print("✅ EMAIL SENT SUCCESSFULLY!")
        except Exception as send_error:
            error_msg = f"❌ EMAIL SEND FAILED: {str(send_error)}"
            logger.error(error_msg)
            print(error_msg)
            raise  # Re-raise the exception to maintain original error handling

        return {
            'success': True,
            'email': protocolo.usuario.email,
            'subject': subject
        }

    except Exception as e:
        logger.error(f"Error sending update email for protocolo {protocolo.id}: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def _prepare_update_email_context(protocolo, old_value, new_value):
    """
    Prepare context data for update notification email templates.

    Args:
        protocolo: Protocolo instance with related objects loaded
        old_value (str): Previous value of doc_sei_num
        new_value (str): New value of doc_sei_num

    Returns:
        dict: Context dictionary for email template rendering
    """
    # Get base context from the original function
    base_context = _prepare_email_context(protocolo)

    # Add update-specific information
    update_context = {
        'update_info': {
            'old_value': old_value or 'Não informado',
            'new_value': new_value or 'Não informado',
            'field_name': 'Número SEI',
            'updated_at': timezone.now(),
            'has_change': old_value != new_value
        },
        'is_update_notification': True
    }

    # Merge contexts
    base_context.update(update_context)
    return base_context


def _prepare_email_context(protocolo):
    """
    Prepare context data for email templates.

    Args:
        protocolo: Protocolo instance with related objects loaded

    Returns:
        dict: Context dictionary for email template rendering
    """
    return {
        'protocolo': protocolo,
        'doc_cod': protocolo.doc_cod,
        'usuario_nome': protocolo.usuario.nome if protocolo.usuario else 'Não informado',
        'usuario_email': protocolo.usuario.email if protocolo.usuario else 'Não informado',
        'created_at': protocolo.created_at,
        'current_year': timezone.now().year,
        'access_password': protocolo.doc_url_cod,  # Access password for protocol editing

        # Related object information with safe defaults
        'unidade': {
            'unidade': protocolo.unidade.unidade if protocolo.unidade else 'Não informado',
            'codigo': protocolo.unidade.codigo if protocolo.unidade else 'Não informado'
        },
        'interessado': {
            'codigo': protocolo.interessado.codigo if protocolo.interessado else 'Não informado',
            'concessao': protocolo.interessado.concessao if protocolo.interessado else 'Não informado'
        },
        'localizacao': {
            'localizacao': protocolo.localizacao.localizacao if protocolo.localizacao else 'Não informado',
            'codigo': protocolo.localizacao.codigo if protocolo.localizacao else 'Não informado'
        },
        'assunto': {
            'descricao': protocolo.assunto.descricao if protocolo.assunto else 'Não informado',
            'codigo': protocolo.assunto.codigo if protocolo.assunto else 'Não informado'
        },
        'servico': {
            'codigo': protocolo.servico_codigo or 'Não informado',
            'tipo': protocolo.servico_tipo or 'Não informado'
        },
        'local': {
            'local': protocolo.local.local if protocolo.local else 'Não informado',
            'codigo': protocolo.local.codigo if protocolo.local else 'Não informado'
        },
        'disciplina': {
            'disciplina': protocolo.disciplina.disciplina if protocolo.disciplina else 'Não informado',
            'codigo': protocolo.disciplina.codigo if protocolo.disciplina else 'Não informado'
        },
        'additional_info': {
            'doc_revisao': protocolo.doc_revisao or 'Não aplicável',
            'doc_sei_num': protocolo.doc_sei_num or 'Não aplicável'
        }
    }